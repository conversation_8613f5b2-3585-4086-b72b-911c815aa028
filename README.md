## User Service Backend (Spring Boot)


### What this project does
- **Load users**: Pull users from `dummyjson.com` and persist them in H2.
- **Search**: Free-text search across `firstName`, `lastName`, and `ssn`.
- **Lookup**: Fetch a user by `id` or `email`.
- **Docs & tooling**: Swagger UI, H2 Console, logging, and tests.

### Tech stack
- **Java 17**, **Spring Boot 3** (Web, Validation)
- **Spring Data JPA** with **H2** (in-memory)
- **Resilience4j** (retry + circuit breaker)
- **OpenAPI/Swagger** via `springdoc-openapi`
- **Lombok** (getters/setters/builders)
- **Gradle** build

---

Swagger UI
http://localhost:8000/swagger-ui/index.html#/user-controller/loadUsers
Use this to load user initially


### Run it locally
Prerequisites:
- Java 17+


The app starts on `http://localhost:8080`.

### Useful URLs
- Swagger UI: `http://localhost:8080/swagger-ui.html`
- OpenAPI JSON: `http://localhost:8080/api-docs`
- H2 Console: `http://localhost:8080/h2-console`
  - JDBC URL: `jdbc:h2:mem:testdb`
  - Username: `sa`
  - Password: (empty)

---

### Configuration
Default values (see `src/main/resources/application.properties`):
- `server.port=8080`
- `external.users.base-url=https://dummyjson.com`
- `external.users.limit=100`
- `spring.h2.console.enabled=true`
- `springdoc.swagger-ui.path=/swagger-ui.html`



---

### Endpoints (quick start)
Base path: `/api/users`

1) Load users from external API into H2
```bash
curl -X POST http://localhost:8080/api/users/load
```

2) Search users (free text)
```bash
curl "http://localhost:8080/api/users/search?query=john"
```

3) Get by id
```bash
curl http://localhost:8080/api/users/1
```

4) Get by email
```bash
curl http://localhost:8080/api/users/email/<EMAIL>
```

Typical responses are JSON. Validation errors and unexpected issues are handled by a global exception handler with clear error payloads.

---

### Run with Docker
1) Build the JAR (see above)
2) Build the image:
```bash
docker build -t backend .
```
3) Run the container:
```bash
docker run -p 8080:8080 backend
```

---
### Project structure (key parts)
```
src/main/java/com/example/backend/
  ├─ controller/UserController.java      # REST endpoints
  ├─ service/UserService.java            # Business logic + external API calls
  ├─ repository/UserRepository.java      # JPA queries
  ├─ model/UserEntity.java               # JPA entity
  ├─ dto/                                # API records
  ├─ config/                             # RestTemplate bean, etc.
  └─ exception/                          # Global exception handling
src/main/resources/
  └─ application.properties              # App configuration
```


---



