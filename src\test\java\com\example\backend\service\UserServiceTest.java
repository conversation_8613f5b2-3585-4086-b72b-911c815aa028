package com.example.backend.service;

import com.example.backend.dto.UserApiResponse;
import com.example.backend.dto.UserDto;
import com.example.backend.exception.ExternalApiException;
import com.example.backend.model.UserEntity;
import com.example.backend.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import static org.mockito.Mockito.verify;


import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.times;

public class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private UserService userService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);

        userService = new UserService(userRepository, restTemplate);
        ReflectionTestUtils.setField(userService, "baseUrl", "https://dummyjson.com");
        ReflectionTestUtils.setField(userService, "userLimit", 10);
    }

    @Test
    void loadUsers_ShouldSaveUsers_WhenApiReturnsData() {
        // Arrange
        UserDto dto = new UserDto(1L, "John", "Doe", null, 30, "<EMAIL>", "123-45-6789");
        UserApiResponse apiResponse = new UserApiResponse(List.of(dto), 1, 0, 10);
        when(restTemplate.getForObject(anyString(), eq(UserApiResponse.class)))
                .thenReturn(apiResponse);

        // Act
        userService.loadUsers();

        // Assert
        verify(userRepository, times(1)).saveAll(anyList());
    }

    @Test
    void loadUsers_ShouldThrowException_WhenApiReturnsNull() {
        when(restTemplate.getForObject(anyString(), eq(UserApiResponse.class)))
                .thenReturn(null);

        assertThrows(ExternalApiException.class, () -> userService.loadUsers());
    }

    @Test
    void searchUsers_ShouldReturnResults_FromRepository() {
        List<UserEntity> mockList = List.of(new UserEntity());
        when(userRepository.searchByFreeText("john")).thenReturn(mockList);

        List<UserEntity> result = userService.searchUsers("John");

        assertEquals(1, result.size());
        verify(userRepository, times(1)).searchByFreeText("john");
    }

    @Test
    void findById_ShouldReturnUser_FromRepository() {
        UserEntity entity = new UserEntity();
        when(userRepository.findById(1L)).thenReturn(Optional.of(entity));

        Optional<UserEntity> result = userService.findById(1L);

        assertTrue(result.isPresent());
        verify(userRepository, times(1)).findById(1L);
    }

    @Test
    void findByEmail_ShouldReturnUser_FromRepository() {
        UserEntity entity = new UserEntity();
        when(userRepository.findByEmailIgnoreCase("<EMAIL>"))
                .thenReturn(Optional.of(entity));

        Optional<UserEntity> result = userService.findByEmail("<EMAIL>");

        assertTrue(result.isPresent());
        verify(userRepository, times(1)).findByEmailIgnoreCase("<EMAIL>");
    }

    @Test
    void loadUsersFallback_ShouldThrowExternalApiException() {
        Throwable cause = new RuntimeException("API Down");
        ExternalApiException exception = assertThrows(ExternalApiException.class,
                () -> userService.loadUsersFallback(cause));

        assertTrue(exception.getMessage().contains("Failed to fetch users"));
    }
}
