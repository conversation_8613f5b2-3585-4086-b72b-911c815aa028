package com.example.backend.repository;

import com.example.backend.model.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends JpaRepository<UserEntity,Long> {
    Optional<UserEntity> findByEmailIgnoreCase(String email);

    @Query("""
        SELECT u FROM UserEntity u
        WHERE LOWER(u.firstName) LIKE %:query%
           OR LOWER(u.lastName) LIKE %:query%
           OR LOWER(u.ssn) LIKE %:query%
    """)
    List<UserEntity> searchByFreeText(String query);
}
