package com.example.backend.service;

import com.example.backend.dto.UserApiResponse;
import com.example.backend.dto.UserDto;
import com.example.backend.exception.ExternalApiException;
import com.example.backend.model.UserEntity;
import com.example.backend.repository.UserRepository;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    @Autowired
    private final UserRepository userRepository;
    @Autowired
    private final RestTemplate restTemplate;

    @Value("${external.users.base-url}")
    private String baseUrl;

    @Value("${external.users.limit}")
    private int userLimit;

    @Transactional
    @Retry(name = "userApi", fallbackMethod = "loadUsersFallback")
    @CircuitBreaker(name = "userApi", fallbackMethod = "loadUsersFallback")
    public void loadUsers() {
        String url = String.format("%s/users?limit=%d", baseUrl, userLimit);
        log.info("Fetching users from external API: {}", url);

        UserApiResponse usersResponse =
                restTemplate.getForObject(url, UserApiResponse.class);

        if (usersResponse == null || usersResponse.users() == null) {
            throw new ExternalApiException("Empty response from external API");
        }

        List<UserEntity> entities = usersResponse.users()
                .stream()
                .map(this::mapToEntity)
                .toList();

        userRepository.saveAll(entities);
        log.info("Successfully loaded {} users into the H2 DB", entities.size());
    }

    public List<UserEntity> searchUsers(String query) {
        return userRepository.searchByFreeText(query.toLowerCase());
    }

    public Optional<UserEntity> findById(Long id) {
        return userRepository.findById(id);
    }

    public Optional<UserEntity> findByEmail(String email) {
        return userRepository.findByEmailIgnoreCase(email);
    }

    private UserEntity mapToEntity(UserDto dto) {
        return UserEntity.builder()
                .id(dto.id())
                .firstName(dto.firstName())
                .lastName(dto.lastName())
                .email(dto.email())
                .ssn(dto.ssn())
                .age(dto.age())
                .build();
    }

    public void loadUsersFallback(Throwable t) {
        log.error("Fallback triggered for loadUsers: {}", t.getMessage());
        throw new ExternalApiException("Failed to fetch users after retries", t);
    }

}
