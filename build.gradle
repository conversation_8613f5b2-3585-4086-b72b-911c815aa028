plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.4'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.example'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-validation'

	// In-memory H2 Database
	runtimeOnly 'com.h2database:h2'

	// Hibernate Search with Lucene (compatible with Hibernate ORM 6.x)


	// Swagger / OpenAPI

	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'


	// Resilience4j for retries and circuit breakers
	implementation 'io.github.resilience4j:resilience4j-spring-boot3:2.2.0'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// Testing
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.mockito:mockito-core:5.12.0'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
}
