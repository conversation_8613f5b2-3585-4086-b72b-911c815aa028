spring.application.name=backend
# Server
server.port=8080

# External API
external.users.base-url=https://dummyjson.com
external.users.limit=100

# H2 Database
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Logging
logging.level.root=INFO
logging.level.com.example=DEBUG

# Swagger
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html

# Resilience4j (Retry + Circuit Breaker)
resilience4j.retry.instances.userApi.maxAttempts=3
resilience4j.retry.instances.userApi.waitDuration=2s
resilience4j.circuitbreaker.instances.userApi.failureRateThreshold=50
resilience4j.circuitbreaker.instances.userApi.waitDurationInOpenState=10s
