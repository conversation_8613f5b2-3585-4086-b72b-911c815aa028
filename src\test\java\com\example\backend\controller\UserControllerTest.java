package com.example.backend.controller;

import com.example.backend.exception.ExternalApiException;
import com.example.backend.model.UserEntity;
import com.example.backend.service.UserService;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class UserControllerTest {
    @Mock
    private UserService userService;

    @InjectMocks
    private UserController userController;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void loadUsers_success_returnsCreated() throws ExternalApiException {
        doNothing().when(userService).loadUsers();

        ResponseEntity<String> response = userController.loadUsers();

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertEquals("Users loaded successfully", response.getBody());
        verify(userService, times(1)).loadUsers();
    }

    @Test
    void loadUsers_externalApiException_propagatesException() throws ExternalApiException {
        doThrow(new ExternalApiException("API error")).when(userService).loadUsers();

        ExternalApiException exception = assertThrows(ExternalApiException.class, () -> {
            userController.loadUsers();
        });

        assertEquals("API error", exception.getMessage());
        verify(userService, times(1)).loadUsers();
    }

    @Test
    void searchUsers_validQuery_returnsUserList() {
        UserEntity user = new UserEntity();
        user.setId(1L);
        user.setFirstName("John");
        user.setLastName("Doe");
        List<UserEntity> userList = List.of(user);

        when(userService.searchUsers("John")).thenReturn(userList);

        ResponseEntity<List<UserEntity>> response = userController.searchUsers("John");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, response.getBody().size());
        verify(userService, times(1)).searchUsers("John");
    }
    @Test
    void findById_existingId_returnsUser() {
        UserEntity user = new UserEntity();
        user.setId(1L);

        when(userService.findById(1L)).thenReturn(Optional.of(user));

        ResponseEntity<UserEntity> response = userController.findById(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(user, response.getBody());
        verify(userService, times(1)).findById(1L);
    }

    @Test
    void findById_nonExistingId_returnsNotFound() {
        when(userService.findById(1L)).thenReturn(Optional.empty());

        ResponseEntity<UserEntity> response = userController.findById(1L);

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        verify(userService, times(1)).findById(1L);
    }

    @Test
    void findByEmail_existingEmail_returnsUser() {
        UserEntity user = new UserEntity();
        user.setEmail("<EMAIL>");

        when(userService.findByEmail("<EMAIL>")).thenReturn(Optional.of(user));

        ResponseEntity<UserEntity> response = userController.findByEmail("<EMAIL>");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(user, response.getBody());
        verify(userService, times(1)).findByEmail("<EMAIL>");
    }

    @Test
    void findByEmail_nonExistingEmail_returnsNotFound() {
        when(userService.findByEmail("<EMAIL>")).thenReturn(Optional.empty());

        ResponseEntity<UserEntity> response = userController.findByEmail("<EMAIL>");

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        verify(userService, times(1)).findByEmail("<EMAIL>");
    }

}
