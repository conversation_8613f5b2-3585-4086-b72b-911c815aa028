package com.example.backend.controller;


import com.example.backend.exception.ExternalApiException;
import com.example.backend.model.UserEntity;
import com.example.backend.service.UserService;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Validated
public class UserController {

    private final UserService userService;

    /**
     * Load users from external API into in-memory H2 DB.
     */
    @PostMapping("/load")
    public ResponseEntity<String> loadUsers() {
        log.info("Request received: Load users from external API");
        try {
            userService.loadUsers();
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body("Users loaded successfully");
        } catch (ExternalApiException ex) {
            log.error("Error loading users: {}", ex.getMessage());
            throw ex; // Will be handled by GlobalExceptionHandler
        }
    }

    /**
     * Search users by free text (firstName, lastName, ssn).
     */
    @GetMapping("/search")
    public ResponseEntity<List<UserEntity>> searchUsers(
            @RequestParam @NotBlank(message = "Search query cannot be blank") String query) {
        log.info("Searching users with query: {}", query);
        List<UserEntity> results = userService.searchUsers(query);
        return ResponseEntity.ok(results);
    }

    /**
     * Find user by ID.
     */
    @GetMapping("/{id}")
    public ResponseEntity<UserEntity> findById(
            @PathVariable @Positive(message = "ID must be positive") Long id) {
        log.info("Fetching user by ID: {}", id);
        return userService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Find user by email.
     */
    @GetMapping("/email/{email}")
    public ResponseEntity<UserEntity> findByEmail(
            @PathVariable @Email(message = "Invalid email format") String email) {
        log.info("Fetching user by email: {}", email);
        return userService.findByEmail(email)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
}
